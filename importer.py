import os
import pandas as pd
import pymysql
from openpyxl import load_workbook

# 修改为你的MySQL数据库连接信息
host = '************'
user = 'lxt'
password = 'Lxt0307+'
database = 'qq_day_sale'

# 遍历文件夹下的所有Excel文件
folder_path = 'D:/bdwp/202507'
excel_files = [f for f in os.listdir(folder_path) if f.endswith('.xlsx') or f.endswith('.xls')]

# 连接到MySQL数据库
connection = pymysql.connect(host=host, user=user, password=password, database=database)
cursor = connection.cursor()

# 你指定的表名
specified_table_name = 'jd_sale'

# 遍历Excel文件并导入数据到MySQL数据库的指定表中
for file in excel_files:
    file_path = os.path.join(folder_path, file)
    wb = load_workbook(file_path)
    ws = wb.active


    # 遍历工作表中的所有行，将数据插入到MySQL数据库的指定表中
    for row in ws.iter_rows(min_row=2, values_only=True):  # 假设第一行是表头，从第二行开始插入数据
        placeholders = ', '.join(['%s'] * len(row))
        sql = f"INSERT INTO {specified_table_name} VALUES ({placeholders})"
        cursor.execute(sql, row)

        print(file)

# 提交事务并关闭连接
connection.commit()
cursor.close()
connection.close()